package cn.iocoder.yudao.module.insurance.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.List;

@ConfigurationProperties(prefix = "insurance")
@Validated
@Data
public class InsuranceProperties {

    private AreaProperties area;
    private ZlbProperties zlb;
    private SsoProperties sso;
    private OauthProperties oauth;

    @Data
    public static class AreaProperties {

        @NotNull(message = "区域模式不能为空")
        private Mode mode;

        private List<Long> idList;

        public enum Mode {
            CENTER, AREA
        }
    }

    @Data
    public static class ZlbProperties {
        private String userInfoKey;
        private String userInfoIv;
        private String regKey;
        private String regIv;
    }

    @Data
    public static class SsoProperties {
        private String anjiUrl;
        private String appKey;
    }

    @Data
    public static class OauthProperties {
        private Boolean enable;
        /**
         * 公司加密根密钥
         */
        private String companyKey;

        /**
         * 公司加密根IV
         */
        private String companyIv;
    }
}
